{"date": "2025-06-19", "total_cars": 149652, "categories": {"1": {"name": "Супер премиум люкс", "count": 1323, "chunks": 1, "chunk_size": 12000, "cache_files": [{"file": "super_priority_1_chunk_1_2025-06-19.json", "chunk": 1, "cars": 1323, "size": 20731038, "size_mb": 19.77}]}, "2": {"name": "Премиум", "count": 31153, "chunks": 3, "chunk_size": 12000, "cache_files": [{"file": "super_priority_2_chunk_1_2025-06-19.json", "chunk": 1, "cars": 12000, "size": 215027628, "size_mb": 205.07}, {"file": "super_priority_2_chunk_2_2025-06-19.json", "chunk": 2, "cars": 12000, "size": 222780213, "size_mb": 212.46}, {"file": "super_priority_2_chunk_3_2025-06-19.json", "chunk": 3, "cars": 7153, "size": 137790524, "size_mb": 131.41}]}, "3": {"name": "Средние", "count": 17685, "chunks": 2, "chunk_size": 12000, "cache_files": [{"file": "super_priority_3_chunk_1_2025-06-19.json", "chunk": 1, "cars": 12000, "size": 235918392, "size_mb": 224.99}, {"file": "super_priority_3_chunk_2_2025-06-19.json", "chunk": 2, "cars": 5685, "size": 106595215, "size_mb": 101.66}]}, "4": {"name": "Бюджетные", "count": 17685, "chunks": 5, "chunk_size": 3537, "cache_files": [{"file": "super_priority_4_chunk_1_2025-06-19.json", "chunk": 1, "cars": 3537, "size": 68945203, "size_mb": 65.75}, {"file": "super_priority_4_chunk_2_2025-06-19.json", "chunk": 2, "cars": 3537, "size": 69737531, "size_mb": 66.51}, {"file": "super_priority_4_chunk_3_2025-06-19.json", "chunk": 3, "cars": 3537, "size": 69951762, "size_mb": 66.71}, {"file": "super_priority_4_chunk_4_2025-06-19.json", "chunk": 4, "cars": 3537, "size": 69267410, "size_mb": 66.06}, {"file": "super_priority_4_chunk_5_2025-06-19.json", "chunk": 5, "cars": 3537, "size": 64612377, "size_mb": 61.62}]}}, "brand_count": 61, "price_ranges": {"1": {"min": 99, "max": 200000, "median": 7800, "count": 1323}, "2": {"min": 11, "max": 99999, "median": 3890, "count": 31153}, "3": {"min": 11, "max": 99999, "median": 2779, "count": 17685}, "4": {"min": 11, "max": 99999, "median": 1430, "count": 99490}}, "year_ranges": {"1": {"min": 1980, "max": 2024, "median": 2018, "count": 1323}, "2": {"min": 1970, "max": 2025, "median": 2019, "count": 31153}, "3": {"min": 1980, "max": 2025, "median": 2020, "count": 17685}, "4": {"min": 1984, "max": 2025, "median": 2019, "count": 99491}}, "generated_at": "2025-06-19 13:56:22", "expires_at": "2025-06-20 13:56:22", "version": "2.0_super_fast"}