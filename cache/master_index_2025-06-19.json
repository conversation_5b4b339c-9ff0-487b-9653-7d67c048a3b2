{"date": "2025-06-19", "total_cars": 149652, "categories": {"1": {"name": "Супер премиум люкс", "count": 777, "chunks": 1, "chunk_size": 12000, "cache_files": [{"file": "super_priority_1_chunk_1_2025-06-19.json", "chunk": 1, "cars": 777, "size": 10710022, "size_mb": 10.21}]}, "2": {"name": "Премиум", "count": 40112, "chunks": 4, "chunk_size": 12000, "cache_files": [{"file": "super_priority_2_chunk_1_2025-06-19.json", "chunk": 1, "cars": 12000, "size": 215027628, "size_mb": 205.07}, {"file": "super_priority_2_chunk_2_2025-06-19.json", "chunk": 2, "cars": 12000, "size": 224356103, "size_mb": 213.96}, {"file": "super_priority_2_chunk_3_2025-06-19.json", "chunk": 3, "cars": 12000, "size": 236671076, "size_mb": 225.71}, {"file": "super_priority_2_chunk_4_2025-06-19.json", "chunk": 4, "cars": 4112, "size": 76875150, "size_mb": 73.31}]}, "3": {"name": "Средние", "count": 3861, "chunks": 1, "chunk_size": 12000, "cache_files": [{"file": "super_priority_3_chunk_1_2025-06-19.json", "chunk": 1, "cars": 3861, "size": 72790115, "size_mb": 69.42}]}, "4": {"name": "Бюджетные", "count": 3861, "chunks": 5, "chunk_size": 773, "cache_files": [{"file": "super_priority_4_chunk_1_2025-06-19.json", "chunk": 1, "cars": 773, "size": 14507481, "size_mb": 13.84}, {"file": "super_priority_4_chunk_2_2025-06-19.json", "chunk": 2, "cars": 773, "size": 14892049, "size_mb": 14.2}, {"file": "super_priority_4_chunk_3_2025-06-19.json", "chunk": 3, "cars": 773, "size": 14959375, "size_mb": 14.27}, {"file": "super_priority_4_chunk_4_2025-06-19.json", "chunk": 4, "cars": 773, "size": 14962334, "size_mb": 14.27}, {"file": "super_priority_4_chunk_5_2025-06-19.json", "chunk": 5, "cars": 769, "size": 13469767, "size_mb": 12.85}]}}, "brand_count": 61, "price_ranges": {"1": {"min": 99, "max": 200000, "median": 22000, "count": 777}, "2": {"min": 11, "max": 99999, "median": 3790, "count": 40112}, "3": {"min": 77, "max": 99999, "median": 2490, "count": 3861}, "4": {"min": 11, "max": 99999, "median": 1450, "count": 104901}}, "year_ranges": {"1": {"min": 1980, "max": 2024, "median": 2019, "count": 777}, "2": {"min": 1970, "max": 2025, "median": 2020, "count": 40112}, "3": {"min": 1989, "max": 2025, "median": 2019, "count": 3861}, "4": {"min": 1980, "max": 2025, "median": 2019, "count": 104902}}, "generated_at": "2025-06-19 13:45:06", "expires_at": "2025-06-20 13:45:06", "version": "2.0_super_fast"}