{"ChevroletGMDaewoo": {"Matiz": 189, "Spark": 2051, "Lacetti": 66, "Aveo": 174, "Cruze": 684, "Tosca": 8, "Alpheon": 147, "Orlando": 697, "Malibu": 1035, "labo": 153, "damas": 160, "Captiva": 198, "Prince": 1, "Trax": 945, "Impala": 122, "Acadia": 6, "Espero": 4, "G2X": 12, "Equinox": 71, "Bolt EV": 67, "Trailblazer": 347, "Traverse": 87, "Colorado": 367, "Camaro": 60, "Winstorm": 16, "볼트 EUV": 19, "Tahoe": 16, "Gentra": 9, "Magnus": 1, "VERITAS": 2, "Statesman": 3, "Tico": 4, "VOLT": 1, "Lemans": 1, "Leganza": 1, "Rezzo": 1, "Super Salon": 1}, "Hyundai": {"Grandeur": 8853, "Sonata": 4446, "AVANTE": 5530, "Equus": 774, "Tucson": 2651, "Santafe": 4359, "Starex": 3004, "Traget XG": 6, "i30": 368, "Marcia": 3, "Genesis": 1597, "Accent": 453, "Dynasty": 13, "Terracan": 28, "Tuscani": 18, "i40": 246, "Veracruz": 128, "Veloster": 376, "Elantra": 3, "Galloper": 46, "Excel": 7, "Aslan": 139, "Ioniq": 192, "Maxcruz": 277, "Pony": 5, "Venue": 329, "Kona": 1292, "Casper": 627, "Nexo": 141, "Staria": 1279, "Palisade": 2675, "Ioniq5": 548, "Solati": 127, "Verna": 14, "Tiburon": 3, "Ioniq6": 150, "ST1": 2, "Santamo": 4, "Atoz": 4, "Lavita": 3, "Click": 3, "BlueOn": 1}, "Renault-KoreaSamsung": {"SM7": 488, "SM5 ": 1070, "SM3": 818, "Twizy": 20, "QM5 ": 213, "QM3": 471, "SM6": 1382, "QM6": 1539, "XM3": 500, "Master": 123, "Arkana": 37, "Captur": 30, "Cilo": 47, "Grand Koleos": 67, "Zoe": 7}, "Kia": {"morning": 3122, "LOTZE": 33, "OPIRUS": 134, "Porte": 137, "Carens": 289, "Canival": 7387, "Sportage": 2642, "Spectra": 3, "Soul": 342, "K5/Optima": 3588, "pride": 262, "Sephia": 1, "K7/Cadenza": 3005, "Sorento": 3964, "K3": 2222, "RAY": 2700, "Potentia": 7, "K9/Quoris": 1195, "Mohave": 1314, "Enterprise": 4, "Stonic": 208, "Bongo III Minibus": 3, "Niro": 905, "Seltos": 1127, "Stinger": 523, "EV6": 506, "K8": 1103, "Parktown": 1, "Credos": 2, "Cerato": 1, "Elan": 1, "Avella": 1, "EV3": 44, "EV9": 51, "Regal": 1, "Retona": 1, "Besta": 1, "Capital": 1, "Tasman": 1}, "Volvo": {"S80": 45, "C30": 9, "S40": 8, "XC90": 184, "XC60": 246, "940": 2, "S60": 106, "V40": 48, "V60": 80, "S70": 1, "S90": 170, "V90": 22, "XC40": 119, "850": 2, "C40": 21, "XC70": 15, "960": 1, "V50": 2, "C70": 1, "740": 1}, "KG_Mobility_Ssangyong": {"Chairman": 228, "Rexton": 2378, "Rodius": 10, "Kyron": 10, "KORANDO": 1626, "Musso": 7, "TIBOLI": 1721, "Torres": 315, "Actyon": 95, "Istana": 1}, "Saab": {"9-3": 12, "900": 3, "9000": 4, "9-5": 3}, "Lincoln": {"MKS": 36, "MKZ": 100, "Town Car": 8, "MKX": 37, "MKC": 46, "Continental": 40, "Navigator": 38, "Aviator": 100, "Corsair": 26, "Nautilus": 43}, "Chrysler": {"Sebring": 3, "300C": 63, "Pacifica": 2, "Crossfire": 6, "200": 19, "Grand Voyager": 7, "PT Crusier": 6}, "Ford": {"Taurus": 52, "Focus": 14, "Fusion": 10, "Escape": 12, "Explorer": 414, "Mustang": 241, "F150": 65, "F350": 6, "E-Series": 6, "Ranger": 70, "Transit": 16, "Expedition": 17, "Mondeo": 49, "Freestyle": 1, "Kuga": 14, "F250": 4, "Bronco": 18, "Econoline": 1, "Five Hundred": 1}, "Audi": {"A6": 956, "A3": 116, "A4": 241, "A8": 171, "A5": 182, "S4": 20, "Q7": 248, "Q5": 229, "S6": 17, "A7": 290, "TT": 27, "S7": 9, "Q2": 20, "RS7": 13, "Q3": 80, "e-tron": 61, "R8": 61, "Q8": 128, "S8": 12, "S3": 4, "TTS": 12, "S5": 5, "RSQ8": 11, "SQ5": 45, "e-tron GT": 9, "Q8 e-tron": 13, "Q4 e-tron": 32, "RS3": 2, "Allroad Quattro": 2, "RS5": 17, "A1": 4, "RS e-tron GT": 2, "SQ8": 3}, "BMW": {"5-Series": 2982, "7-Series": 736, "3-Series": 1163, "Gran Turismo": 732, "X3": 591, "1-Series": 345, "X5": 877, "Z4": 115, "X1": 223, "i3": 25, "2-Series": 145, "4-Series": 389, "6-Series": 85, "X6": 610, "Z3": 6, "X6M": 46, "M6": 16, "M3": 75, "M4": 122, "8-Series": 58, "X4": 489, "M5": 74, "M2": 48, "X7": 420, "X4M": 27, "M8": 12, "i8": 29, "X2 (F39)": 42, "X5M": 27, "XM": 21, "i4": 71, "iX3": 44, "iX": 14, "M Coupe/Roadster": 1, "Z8": 1, "i5": 16, "X3M": 11, "i7": 10, "1M": 2, "iX1": 4}, "Mercedes-Benz": {"E-Class": 3358, "B-Class": 58, "CLS-Class": 598, "My B": 6, "S-Class": 1979, "R-Class": 4, "C-Class": 952, "CL-Class": 25, "SLK-Class": 60, "M-class": 64, "CLK-Class": 4, "A-Class": 436, "GLK-Class": 64, "CLA-Class": 304, "GLA-Class": 253, "SL-Class": 65, "SEL/SEC": 12, "GLE-Class": 712, "GLC-Class": 720, "EQA": 70, "GL-Class": 7, "Sprinter": 50, "GLS-Class": 213, "SLC-Class": 24, "G-Class": 264, "EQB": 56, "Others": 1, "V-Class": 23, "EQE": 138, "AMG GT": 233, "EQS": 71, "SLS AMG": 5, "GLB-Class": 172, "190-Class": 2, "EQC": 8, "CLE-Class": 45}, "Mini": {"Cooper": 943, "Clubman": 316, "Countryman": 525, "Coupe": 25, "Paceman": 15, "Cooper Convertible": 134, "Rover Mini": 4, "Roadster": 13}, "Peugeot": {"308": 58, "307": 1, "207": 20, "407": 1, "508": 70, "206": 7, "3008": 91, "5008": 69, "208": 26, "2008": 99, "Expert": 2, "408": 12, "RCZ": 6}, "Infiniti": {"G": 54, "M": 24, "FX": 26, "Q50": 84, "EX": 7, "Q30": 29, "QX70": 8, "QX50": 30, "QX60": 49, "Q60": 3, "QX30": 8, "Q70": 14, "QX": 2, "JX": 3, "QX80": 2}, "Nissan": {"Altima": 115, "Cube": 49, "Murano": 18, "Leaf": 11, "Maxima": 25, "Frontier": 3, "Pathfinder": 12, "370Z": 13, "Skyline": 3, "Rogue": 3, "Elgrand": 2, "300ZX": 2, "Juke": 13, "March": 2, "350Z": 1, "Figaro": 1, "Quest": 1, "X-Trail": 14, "Qashqai": 3, "GT-R": 1}, "Honda": {"Legend": 16, "Civic": 16, "Accord": 134, "Pilot": 55, "CR-V": 79, "Odyssey": 69, "CR-Z": 2, "Crosstour": 2, "Insight": 3, "S2000": 2, "HR-V": 3, "N-BOX": 1, "S660": 1}, "Others": {"Others": 70}, "Volkswagen": {"Beatle": 60, "Passat": 162, "Golf": 280, "CC": 68, "Touareg": 83, "Polo": 39, "Phaeton": 16, "Scirocco": 24, "Arteon": 112, "Tiguan": 406, "Jetta": 135, "T-Roc": 21, "ID.4": 33, "EOS": 2, "ID.5": 1, "Atlas": 1, "Multivan": 2}, "Citroen-DS": {"DS3": 18, "C4 Picasso": 37, "C3 Aircross": 10, "C4 SpaceTourer": 4, "C4 CACTUS": 26, "DS5": 5, "C5 Aircross": 6, "DS7": 7, "DS4": 4}, "Lexus": {"ES": 385, "IS": 68, "LS": 112, "CT200h": 27, "NX": 146, "RC": 6, "UX": 48, "GS": 21, "RX": 116, "SC": 6, "RZ": 2, "LM": 8, "LC": 4, "LX": 3}, "Toyota": {"bB": 9, "Camry": 185, "Venza": 1, "Prius": 127, "Crown": 18, "Estima": 2, "Sienna": 137, "AYGO": 2, "86": 20, "Celica": 2, "Tundra": 8, "FJ Cruiser": 15, "Supra": 2, "Yaris(Vitz)": 1, "RAV4": 118, "Alphard": 36, "Tacoma": 3, "Highlander": 12, "Avalon": 19, "Soarer": 1, "Corolla": 1, "iQ": 1, "MR-S": 1, "Aristo": 1, "Wish": 1, "Mark2": 1, "Noah": 1}, "Jaguar": {"XF": 143, "X-TYPE": 5, "S-TYPE": 5, "XJ": 88, "XE": 92, "Daimler": 5, "F-PACE": 83, "XK8": 1, "E-PACE": 32, "XJ-8": 6, "F-TYPE": 57, "XKR": 3, "Others": 1, "XJ-6": 7, "I-PACE": 2, "XJR": 1, "XK": 1}, "Jeep": {"Compass": 72, "Cherokee": 332, "Renegade": 225, "Wrangler": 410, "Commander": 3, "Gladiator": 65, "Avenger": 2}, "DFSK": {"C31": 1, "Fengon ix5": 2, "C35": 11, "C32": 1}, "Smart": {"Fortwo": 48, "Forfour": 3, "Roadster": 1}, "Acura": {"TL": 4}, "Land Rover": {"Freelander": 11, "Discovery": 339, "Discovery Sport": 390, "Range Rover Evoque": 251, "Range Rover": 335, "Range Rover Sport": 254, "Range Rover Velar": 99, "Defender": 95}, "Mazda": {"Mazda 3": 2, "MX-5 Miata": 11, "Eunons": 1, "RX-7": 1}, "Suzuki": {"Alto Lapin": 5, "HUSTLER": 9, "ALTO": 1, "Jimny": 12, "Ignis": 1, "Cappucino": 1}, "Fiat": {"Feelmont": 6, "500X": 23, "500": 52, "124": 4, "Ducato": 1}, "Subaru": {"R1": 1, "Legacy": 3, "Impreza": 5, "Forester": 4, "Outback": 2, "Levorg": 1}, "Cadillac": {"CTS": 56, "SRX": 11, "ATS": 23, "Escalade": 174, "CT6": 62, "XT5": 36, "XT4": 7, "Concour": 3, "CTS-V": 5, "STS": 1, "XT6": 13, "CT5": 9, "CT4": 4, "Lyriq": 1, "Fleetwood": 1, "DeVille": 1, "SeVille": 1, "DTS": 2}, "Mercury": {"Sable": 1}, "Dodge": {"Durango": 4, "Dakoda": 8, "Ram Van": 2, "Ram Pick Up": 50, "Charger": 4, "Challenger": 17, "Caliber": 4, "Nitro": 1, "Others": 1, "Viper": 1}, "Porsche": {"Cayenne": 535, "Panamera": 422, "Cayman": 15, "Boxster": 67, "Macan": 178, "718": 216, "911": 242, "Taycan": 138}, "Chevrolet": {"Blazer": 2, "Express Van": 76, "Surburban": 16, "Others": 2, "Camaro": 5, "Silverado": 5, "Corvette": 18, "Chevy Van": 7, "Tahoe": 2, "Astro Van": 6, "Avalanche": 3, "Colorado": 2, "Tracker": 1}, "Genesis": {"G80": 3385, "EQ900": 750, "G70": 961, "G90": 938, "GV80": 1427, "GV70": 926, "GV60": 76}, "Daihatsu": {"Copen": 13, "Mira": 2, "Tanto": 1, "Mira Gino": 1, "MATERIA": 1}, "Hummer": {"H3": 13, "H2": 33, "H1": 1}, "Maserati": {"Ghibli": 240, "Coupe": 2, "Quattroporte": 132, "Levante": 136, "GranTurismo": 19, "Gran Cabrio": 4, "MC20": 2, "Grecale": 9, "Spyder": 1}, "Bentley": {"Continental": 112, "Flying Spur": 100, "Bentayga": 56, "Mulsanne": 9, "Brooklands": 1}, "Mitsubishi": {"Lancer Evolution": 1, "Eclipse": 1}, "Mitsuoka": {"Galue": 4, "Le-seyde": 1}, "Tesla": {"Model 3": 415, "Model Y": 297, "Model S": 30, "Model X": 30, "Cybertruck": 2}, "Polestar": {"Polestar 2": 45, "Polestar 4": 2}, "GMC": {"Canyon": 1, "Savana": 11, "Sierra": 20, "Yukon": 3, "Hummer EV": 2, "Vandura": 1}, "Maybach": {"62": 5, "57": 3, "57s": 2, "62s": 4}, "Lotus": {"Elise": 2, "Evora": 6, "Exige": 3, "Emira": 1, "Eletre": 1}, "Astonmartin": {"Rapide": 11, "DB9": 2, "Vantage": 12, "DB11": 10, "DBX": 10, "Vanquish": 6}, "Ferrari": {"California": 19, "599": 2, "FF": 2, "458": 19, "488": 25, "GTC4 Lusso": 3, "F12 Berlinetta": 3, "296": 9, "Roma": 15, "812": 10, "F8": 13, "SF90": 10, "F430": 9, "Portofino": 17, "360": 3, "612": 4, "F355": 2, "308": 1, "328": 1, "Purosangue": 1, "575M": 2}, "Rolls-Royce": {"Ghost": 53, "Wraith": 22, "Dawn": 11, "Cullinan": 41, "Phantom": 15, "Spectre": 3}, "Mclaren": {"650S": 10, "570S": 15, "600LT": 5, "720S": 12, "750S": 4, "765LT": 3, "675LT": 1, "GT": 2, "Senna": 1, "570GT": 1, "Artura": 4}, "Lamborghini": {"Huracan": 41, "Gallardo": 9, "Aventador": 16, "Urus": 45, "Murcielago": 1}, "etc": {"Others": 18}, "Baic Yinxiang": {"CK MINI Truck": 3, "Kenbo 600": 3, "CK Mini Van": 3}, "Alfa Romeo": {"Brera": 1}, "Opel": {"SpeedSter": 1}, "Ineos": {"Grenadier": 2}, "Scion": {"xB": 1}}