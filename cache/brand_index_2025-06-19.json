{"ChevroletGMDaewoo": {"priority": 4, "count": 7726, "models": ["<PERSON><PERSON>", "Spark", "<PERSON><PERSON>", "Aveo", "Cruze", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Orlando", "Malibu", "labo", "damas", "Captiva", "Prince", "Trax", "Impala", "Acadia", "Espero", "G2X", "Equinox", "Bolt EV", "Trailblazer", "Traverse", "Colorado", "Camaro", "Winstorm", "볼트 EUV", "Tahoe", "Gentra", "<PERSON>", "VERITAS", "Statesman", "Tico", "VOLT", "<PERSON><PERSON>", "Leganza", "Rezzo", "Super Salon"], "price_min": 69, "price_max": 9999, "year_min": 1990, "year_max": 2025}, "Hyundai": {"priority": 4, "count": 40724, "models": ["Grandeur", "Sonata", "AVANTE", "<PERSON>qu<PERSON>", "Tucson", "Santafe", "Starex", "Traget XG", "i30", "<PERSON>", "Genesis", "Accent", "Dynasty", "Terracan", "<PERSON><PERSON><PERSON>", "i40", "Veracruz", "<PERSON><PERSON><PERSON>", "Elantra", "G<PERSON><PERSON>", "Excel", "<PERSON><PERSON>", "Ioniq", "Maxcruz", "Pony", "Venue", "<PERSON><PERSON>", "<PERSON>", "Nexo", "Staria", "Palisade", "Ioniq5", "Solati", "<PERSON><PERSON><PERSON>", "Tiburon", "Ioniq6", "ST1", "Santamo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Click", "BlueOn"], "price_min": 11, "price_max": 11111, "year_min": 1984, "year_max": 2025}, "Renault-KoreaSamsung": {"priority": 4, "count": 6812, "models": ["SM7", "SM5 ", "SM3", "Twizy", "QM5 ", "QM3", "SM6", "QM6", "XM3", "Master", "Arkana", "Captur", "<PERSON><PERSON>", "Grand Koleos", "<PERSON>"], "price_min": 61, "price_max": 9999, "year_min": 1998, "year_max": 2025}, "Kia": {"priority": 4, "count": 36832, "models": ["morning", "LOTZE", "OPIRUS", "Porte", "Carens", "Canival", "Sportage", "Spectra", "Soul", "K5/Optima", "pride", "Sephia", "K7/Cadenza", "Sorento", "K3", "RAY", "Potentia", "K9/Quoris", "<PERSON><PERSON>", "Enterprise", "Stonic", "Bongo III Minibus", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "EV6", "K8", "Parktown", "<PERSON><PERSON><PERSON>", "Cerato", "<PERSON><PERSON>", "<PERSON><PERSON>", "EV3", "EV9", "Regal", "Retona", "<PERSON><PERSON>", "Capital", "Tasman"], "price_min": 20, "price_max": 99999, "year_min": 1988, "year_max": 2025}, "Volvo": {"priority": 3, "count": 1083, "models": ["S80", "C30", "S40", "XC90", "XC60", "940", "S60", "V40", "V60", "S70", "S90", "V90", "XC40", "850", "C40", "XC70", "960", "V50", "C70", "740"], "price_min": 160, "price_max": 9999, "year_min": 1990, "year_max": 2025}, "KG_Mobility_Ssangyong": {"priority": 4, "count": 6391, "models": ["Chairman", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "KORANDO", "<PERSON><PERSON>", "TIBOLI", "<PERSON>", "Actyon", "Istana"], "price_min": 129, "price_max": 16633, "year_min": 1992, "year_max": 2025}, "Saab": {"priority": 4, "count": 22, "models": ["9-3", "900", "9000", "9-5"], "price_min": 250, "price_max": 2099, "year_min": 1995, "year_max": 2008}, "Lincoln": {"priority": 2, "count": 474, "models": ["MKS", "MKZ", "Town Car", "MKX", "MKC", "Continental", "Navigator", "Aviator", "Corsair", "Na<PERSON><PERSON>"], "price_min": 99, "price_max": 10100, "year_min": 1995, "year_max": 2025}, "Chrysler": {"priority": 3, "count": 106, "models": ["Sebring", "300C", "Pacifica", "Crossfire", "200", "Grand Voyager", "PT Crusier"], "price_min": 195, "price_max": 9999, "year_min": 2001, "year_max": 2016}, "Ford": {"priority": 4, "count": 1011, "models": ["<PERSON><PERSON>", "Focus", "Fusion", "Escape", "Explorer", "Mustang", "F150", "F350", "E-Series", "<PERSON>", "Transit", "Expedition", "Mondeo", "Freestyle", "<PERSON><PERSON>", "F250", "Bronco", "Econoline", "Five Hundred"], "price_min": 189, "price_max": 22000, "year_min": 1980, "year_max": 2025}, "Audi": {"priority": 2, "count": 3042, "models": ["A6", "A3", "A4", "A8", "A5", "S4", "Q7", "Q5", "S6", "A7", "TT", "S7", "Q2", "RS7", "Q3", "e-tron", "R8", "Q8", "S8", "S3", "TTS", "S5", "RSQ8", "SQ5", "e-tron GT", "Q8 e-tron", "Q4 e-tron", "RS3", "Allroad Quattro", "RS5", "A1", "RS e-tron GT", "SQ8"], "price_min": 11, "price_max": 99999, "year_min": 2000, "year_max": 2025}, "BMW": {"priority": 2, "count": 10704, "models": ["5-Series", "7-Series", "3-Series", "Gran Turismo", "X3", "1-Series", "X5", "Z4", "X1", "i3", "2-Series", "4-Series", "6-Series", "X6", "Z3", "X6M", "M6", "M3", "M4", "8-Series", "X4", "M5", "M2", "X7", "X4M", "M8", "i8", "X2 (F39)", "X5M", "XM", "i4", "iX3", "iX", "M Coupe/Roadster", "Z8", "i5", "X3M", "i7", "1M", "iX1"], "price_min": 11, "price_max": 99999, "year_min": 1984, "year_max": 2025}, "Mercedes-Benz": {"priority": 2, "count": 11056, "models": ["E-Class", "B-Class", "CLS-Class", "My B", "S-Class", "R-Class", "C-Class", "CL-Class", "SLK-Class", "M-class", "CLK-Class", "A-Class", "GLK-Class", "CLA-Class", "GLA-Class", "SL-Class", "SEL/SEC", "GLE-Class", "GLC-Class", "EQA", "GL-Class", "Sprinter", "GLS-Class", "SLC-Class", "G-Class", "EQB", "Others", "V-Class", "EQE", "AMG GT", "EQS", "SLS AMG", "GLB-Class", "190-Class", "EQC", "CLE-Class"], "price_min": 11, "price_max": 99999, "year_min": 1977, "year_max": 2025}, "Mini": {"priority": 4, "count": 1975, "models": ["<PERSON>", "Clubman", "Countryman", "Coupe", "Paceman", "Cooper Convertible", "Rover Mini", "Roadster"], "price_min": 99, "price_max": 9999, "year_min": 1991, "year_max": 2025}, "Peugeot": {"priority": 4, "count": 462, "models": ["308", "307", "207", "407", "508", "206", "3008", "5008", "208", "2008", "Expert", "408", "RCZ"], "price_min": 199, "price_max": 9999, "year_min": 2005, "year_max": 2024}, "Infiniti": {"priority": 2, "count": 343, "models": ["G", "M", "FX", "Q50", "EX", "Q30", "QX70", "QX50", "QX60", "Q60", "QX30", "Q70", "QX", "JX", "QX80"], "price_min": 230, "price_max": 9999, "year_min": 2006, "year_max": 2020}, "Nissan": {"priority": 3, "count": 292, "models": ["Altima", "C<PERSON>", "<PERSON><PERSON>", "Leaf", "Maxima", "Frontier", "Pathfinder", "370Z", "Skyline", "Rogue", "<PERSON><PERSON><PERSON>", "300ZX", "<PERSON><PERSON>", "March", "350Z", "Figaro", "Quest", "X-Trail", "<PERSON><PERSON><PERSON><PERSON>", "GT-R"], "price_min": 180, "price_max": 40000, "year_min": 1989, "year_max": 2019}, "Honda": {"priority": 3, "count": 383, "models": ["Legend", "Civic", "Accord", "Pilot", "CR-V", "Odyssey", "CR-Z", "Crosstour", "Insight", "S2000", "HR-V", "N-BOX", "S660"], "price_min": 150, "price_max": 9999, "year_min": 1998, "year_max": 2024}, "Others": {"priority": 4, "count": 70, "models": ["Others"], "price_min": 99, "price_max": 2690, "year_min": 2018, "year_max": 2023}, "Volkswagen": {"priority": 4, "count": 1445, "models": ["<PERSON><PERSON>", "Passat", "Golf", "CC", "Touareg", "Polo", "Ph<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Arteon", "Tiguan", "<PERSON><PERSON>", "T-Roc", "ID.4", "EOS", "ID.5", "Atlas", "Multivan"], "price_min": 11, "price_max": 33333, "year_min": 1994, "year_max": 2024}, "Citroen-DS": {"priority": 4, "count": 117, "models": ["DS3", "C4 Picasso", "C3 Aircross", "C4 SpaceTourer", "C4 CACTUS", "DS5", "C5 Aircross", "DS7", "DS4"], "price_min": 330, "price_max": 9999, "year_min": 2013, "year_max": 2023}, "Lexus": {"priority": 2, "count": 952, "models": ["ES", "IS", "LS", "CT200h", "NX", "RC", "UX", "GS", "RX", "SC", "RZ", "LM", "LC", "LX"], "price_min": 259, "price_max": 99999, "year_min": 1999, "year_max": 2025}, "Toyota": {"priority": 3, "count": 725, "models": ["bB", "Cam<PERSON>", "Venza", "Prius", "Crown", "Estima", "Sienna", "AYGO", "86", "Celica", "<PERSON><PERSON>", "FJ Cruiser", "<PERSON><PERSON>", "<PERSON><PERSON>(Vitz)", "RAV4", "Alphard", "Tacoma", "Highlander", "Avalon", "Soarer", "<PERSON><PERSON><PERSON>", "iQ", "MR-S", "Aristo", "Wish", "Mark2", "<PERSON>"], "price_min": 270, "price_max": 99999, "year_min": 1995, "year_max": 2025}, "Jaguar": {"priority": 2, "count": 532, "models": ["XF", "X-TYPE", "S-TYPE", "XJ", "XE", "Daimler", "F-PACE", "XK8", "E-PACE", "XJ-8", "F-TYPE", "XKR", "Others", "XJ-6", "I-PACE", "XJR", "XK"], "price_min": 190, "price_max": 29000, "year_min": 1970, "year_max": 2023}, "Jeep": {"priority": 3, "count": 1109, "models": ["<PERSON>mp<PERSON>", "Cherokee", "Renegade", "<PERSON><PERSON><PERSON>", "Commander", "Gladiator", "Avenger"], "price_min": 77, "price_max": 9999, "year_min": 1992, "year_max": 2025}, "DFSK": {"priority": 4, "count": 15, "models": ["C31", "Fengon ix5", "C35", "C32"], "price_min": 350, "price_max": 9999, "year_min": 2017, "year_max": 2024}, "Smart": {"priority": 4, "count": 52, "models": ["Fortwo", "Forfour", "Roadster"], "price_min": 450, "price_max": 8590, "year_min": 2002, "year_max": 2019}, "Acura": {"priority": 2, "count": 4, "models": ["TL"], "price_min": 390, "price_max": 1490, "year_min": 2004, "year_max": 2011}, "Land Rover": {"priority": 2, "count": 1774, "models": ["Freelander", "Discovery", "Discovery Sport", "Range Rover Evoque", "Range Rover", "Range Rover Sport", "Range Rover Velar", "Defender"], "price_min": 430, "price_max": 99999, "year_min": 1992, "year_max": 2025}, "Mazda": {"priority": 3, "count": 15, "models": ["Mazda 3", "MX-5 Miata", "<PERSON><PERSON><PERSON>", "RX-7"], "price_min": 480, "price_max": 6400, "year_min": 1996, "year_max": 2021}, "Suzuki": {"priority": 4, "count": 29, "models": ["<PERSON>", "HUSTLER", "ALTO", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cappucino"], "price_min": 590, "price_max": 7090, "year_min": 1995, "year_max": 2025}, "Fiat": {"priority": 4, "count": 86, "models": ["Feelmont", "500X", "500", "124", "Ducato"], "price_min": 380, "price_max": 10500, "year_min": 2012, "year_max": 2023}, "Subaru": {"priority": 3, "count": 16, "models": ["R1", "Legacy", "<PERSON><PERSON>rez<PERSON>", "<PERSON><PERSON>", "Outback", "Levorg"], "price_min": 370, "price_max": 4500, "year_min": 2006, "year_max": 2015}, "Cadillac": {"priority": 2, "count": 410, "models": ["CTS", "SRX", "ATS", "Escalade", "CT6", "XT5", "XT4", "Concour", "CTS-V", "STS", "XT6", "CT5", "CT4", "Lyriq", "Fleetwood", "DeVille", "SeVille", "DTS"], "price_min": 270, "price_max": 23500, "year_min": 1996, "year_max": 2025}, "Mercury": {"priority": 4, "count": 1, "models": ["Sable"], "price_min": 800, "price_max": 800, "year_min": 2005, "year_max": 2005}, "Dodge": {"priority": 3, "count": 92, "models": ["Durango", "Dakoda", "<PERSON>", "<PERSON> Pick Up", "Charger", "Challenger", "Cal<PERSON>", "<PERSON><PERSON>", "Others", "Viper"], "price_min": 300, "price_max": 18500, "year_min": 1997, "year_max": 2025}, "Porsche": {"priority": 2, "count": 1813, "models": ["Cayenne", "Panamera", "Cayman", "Boxster", "<PERSON><PERSON>", "718", "911", "Taycan"], "price_min": 680, "price_max": 99999, "year_min": 1991, "year_max": 2025}, "Chevrolet": {"priority": 4, "count": 145, "models": ["Blazer", "Express Van", "Surburban", "Others", "Camaro", "Silverado", "Corvette", "Chevy Van", "Tahoe", "<PERSON><PERSON>", "Avalanche", "Colorado", "Tracker"], "price_min": 450, "price_max": 15500, "year_min": 1985, "year_max": 2023}, "Genesis": {"priority": 2, "count": 8463, "models": ["G80", "EQ900", "G70", "G90", "GV80", "GV70", "GV60"], "price_min": 11, "price_max": 20000, "year_min": 2016, "year_max": 2025}, "Daihatsu": {"priority": 4, "count": 18, "models": ["<PERSON><PERSON>", "Mira", "<PERSON><PERSON>", "Mira Gino", "MATERIA"], "price_min": 890, "price_max": 2700, "year_min": 2003, "year_max": 2016}, "Hummer": {"priority": 4, "count": 47, "models": ["H3", "H2", "H1"], "price_min": 999, "price_max": 40000, "year_min": 2003, "year_max": 2009}, "Maserati": {"priority": 2, "count": 545, "models": ["<PERSON><PERSON><PERSON><PERSON>", "Coupe", "Quattroporte", "Levante", "GranTurismo", "Gran Cabrio", "MC20", "Grecale", "<PERSON>der"], "price_min": 539, "price_max": 99999, "year_min": 2004, "year_max": 2024}, "Bentley": {"priority": 1, "count": 278, "models": ["Continental", "Flying Spur", "Bentayga", "<PERSON><PERSON><PERSON>", "Brooklands"], "price_min": 1200, "price_max": 99999, "year_min": 1993, "year_max": 2024}, "Mitsubishi": {"priority": 3, "count": 2, "models": ["Lancer Evolution", "Eclipse"], "price_min": 530, "price_max": 1899, "year_min": 2009, "year_max": 2011}, "Mitsuoka": {"priority": 4, "count": 5, "models": ["Galue", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "price_min": 2990, "price_max": 8500, "year_min": 2001, "year_max": 2014}, "Tesla": {"priority": 4, "count": 774, "models": ["Model 3", "Model Y", "Model S", "Model X", "Cybertruck"], "price_min": 2440, "price_max": 99999, "year_min": 2017, "year_max": 2025}, "Polestar": {"priority": 4, "count": 47, "models": ["Polestar 2", "Polestar 4"], "price_min": 2750, "price_max": 9999, "year_min": 2022, "year_max": 2025}, "GMC": {"priority": 3, "count": 38, "models": ["Canyon", "<PERSON><PERSON>", "Sierra", "Yukon", "Hummer EV", "<PERSON><PERSON><PERSON>"], "price_min": 1170, "price_max": 24000, "year_min": 1995, "year_max": 2024}, "Maybach": {"priority": 1, "count": 14, "models": ["62", "57", "57s", "62s"], "price_min": 4000, "price_max": 22000, "year_min": 2004, "year_max": 2011}, "Lotus": {"priority": 4, "count": 13, "models": ["<PERSON>", "Evor<PERSON>", "Exige", "Emira", "Eletre"], "price_min": 5500, "price_max": 19200, "year_min": 2007, "year_max": 2025}, "Astonmartin": {"priority": 4, "count": 51, "models": ["Rapide", "DB9", "Vantage", "DB11", "DBX", "<PERSON><PERSON><PERSON>"], "price_min": 4980, "price_max": 45000, "year_min": 2010, "year_max": 2023}, "Ferrari": {"priority": 1, "count": 170, "models": ["California", "599", "FF", "458", "488", "GTC4 Lusso", "F12 Berlinetta", "296", "Roma", "812", "F8", "SF90", "F430", "Portofino", "360", "612", "F355", "308", "328", "<PERSON><PERSON><PERSON><PERSON>", "575M"], "price_min": 4990, "price_max": 99999, "year_min": 1989, "year_max": 2024}, "Rolls-Royce": {"priority": 1, "count": 145, "models": ["Ghost", "<PERSON><PERSON>", "Dawn", "Cullinan", "Phantom", "<PERSON><PERSON><PERSON>"], "price_min": 9350, "price_max": 99999, "year_min": 1980, "year_max": 2024}, "Mclaren": {"priority": 1, "count": 58, "models": ["650S", "570S", "600LT", "720S", "750S", "765LT", "675LT", "GT", "<PERSON><PERSON>", "570GT", "<PERSON><PERSON>"], "price_min": 9450, "price_max": 200000, "year_min": 2015, "year_max": 2024}, "Lamborghini": {"priority": 1, "count": 112, "models": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Aventador", "Urus", "<PERSON><PERSON><PERSON><PERSON>"], "price_min": 99, "price_max": 99999, "year_min": 2006, "year_max": 2024}, "etc": {"priority": 4, "count": 18, "models": ["Others"], "price_min": 150, "price_max": 5000, "year_min": 2017, "year_max": 2023}, "Baic Yinxiang": {"priority": 4, "count": 9, "models": ["CK MINI Truck", "Kenbo 600", "CK Mini Van"], "price_min": 140, "price_max": 9990, "year_min": 2016, "year_max": 2017}, "Alfa Romeo": {"priority": 4, "count": 1, "models": ["<PERSON><PERSON>"], "price_min": 2490, "price_max": 2490, "year_min": 2006, "year_max": 2006}, "Opel": {"priority": 4, "count": 1, "models": ["SpeedSter"], "price_min": 3800, "price_max": 3800, "year_min": 2002, "year_max": 2002}, "Ineos": {"priority": 4, "count": 2, "models": ["Grenadier"], "price_min": 12880, "price_max": 13990, "year_min": 2024, "year_max": 2025}, "Scion": {"priority": 4, "count": 1, "models": ["xB"], "price_min": 590, "price_max": 590, "year_min": 2008, "year_max": 2008}}