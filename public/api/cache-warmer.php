<?php
/**
 * 🚀 МОЩНАЯ СИСТЕМА КЭШИРОВАНИЯ для 4-уровневой категоризации автомобилей
 *
 * Этот скрипт выполняет полную обработку CSV файла и создает:
 * 1. Разбивку всей базы на 4 категории приоритета (1-супер премиум, 2-премиум, 3-средние, 4-бюджетные)
 * 2. Кэширование всех возможных марок автомобилей для максимально быстрого поиска
 * 3. Индексы по маркам, моделям, годам, ценам для мгновенного поиска
 * 4. Предзагруженные фильтры для каждой категории
 *
 * ЦЕЛЬ: Обеспечить максимально быстрый поиск автомобилей через предзагруженные кэши
 */

// Увеличиваем лимиты для обработки больших файлов (2.5GB+)
ini_set('max_execution_time', 1200); // 20 минут
ini_set('memory_limit', '8G'); // 8GB для больших CSV файлов

// КРИТИЧНО: Отключаем отображение ошибок чтобы не ломать JSON
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Подключаем только нужные функции из encar-proxy.php без выполнения основной логики
define('CACHE_WARMER_MODE', true); // Флаг для предотвращения выполнения основной логики в encar-proxy.php

// Устанавливаем заголовки для JSON ответа
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// Логирование запросов
function logDebug($message) {
    $debug = isset($_GET['debug']) && $_GET['debug'] == 1;
    if ($debug) {
        $logFile = __DIR__ . '/logs/debug.log';
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logFile, "[$timestamp] [Cache Warmer] $message\n", FILE_APPEND | LOCK_EX);
    }
}

// Функция для получения курсов валют
function getExchangeRates() {
    static $cachedRates = null;
    static $cacheTime = 0;

    // Кэшируем курсы на 1 час
    if ($cachedRates !== null && (time() - $cacheTime) < 3600) {
        return $cachedRates;
    }

    $rates = [
        'KRW_to_RUB' => 0.075, // Примерный курс корейской воны к рублю
        'USD_rate' => 95.0     // Примерный курс доллара к рублю
    ];

    $cachedRates = $rates;
    $cacheTime = time();

    return $rates;
}

// Функция для конвертации цены из корейских вон в доллары
function convertKRWToUSD($priceInKRW, $rates) {
    if (empty($priceInKRW) || $priceInKRW <= 0) {
        return 0;
    }

    // Исключаем забронированные и лизинговые автомобили
    if ($priceInKRW === 999999 || $priceInKRW === 333333) {
        return 0;
    }

    // Проверяем, не является ли цена ID автомобиля
    if ($priceInKRW > 50000) {
        return 0;
    }

    // Формула: цена_в_сокращенном_виде × 10,000 → воны → рубли → доллары
    $realPriceInKRW = $priceInKRW * 10000;
    $priceInRUB = $realPriceInKRW * $rates['KRW_to_RUB'];
    $priceInUSD = $priceInRUB / $rates['USD_rate'];

    $result = round($priceInUSD, 0);

    // Проверяем результат конвертации
    if ($result > 500000) {
        return 0;
    }

    return $result;
}

// Функция для перевода марок автомобилей
function translateBrand($brand) {
    if (empty($brand)) return '';

    $brandTranslations = [
        '현대' => 'Hyundai',
        '기아' => 'Kia',
        '쌍용' => 'SsangYong',
        '르노삼성' => 'Renault Samsung',
        '제네시스' => 'Genesis',
        '쉐보레' => 'Chevrolet',
        '대우' => 'Daewoo',
        '쉐보레대우' => 'Chevrolet',
        '토요타' => 'Toyota',
        '닛산' => 'Nissan',
        '혼다' => 'Honda',
        'BMW' => 'BMW',
        '벤츠' => 'Mercedes-Benz',
        '아우디' => 'Audi',
        '폭스바겐' => 'Volkswagen',
        '볼보' => 'Volvo',
        '포드' => 'Ford',
        '렉서스' => 'Lexus',
        '미니' => 'MINI',
        '포르쉐' => 'Porsche',
        '랜드로버' => 'Land Rover',
        '재규어' => 'Jaguar',
        '크라이슬러' => 'Chrysler',
        '지프' => 'Jeep',
        '마세라티' => 'Maserati',
        '벤틀리' => 'Bentley',
        '페라리' => 'Ferrari',
        '람보르기니' => 'Lamborghini'
    ];

    return $brandTranslations[$brand] ?? $brand;
}

// Функция для перевода моделей автомобилей
function translateModel($brand, $model) {
    if (empty($model)) return '';

    $modelTranslations = [
        'Hyundai' => [
            '쏘나타' => 'Sonata',
            '아반떼' => 'Avante/Elantra',
            '그랜저' => 'Grandeur/Azera',
            '싼타페' => 'Santa Fe',
            '투싼' => 'Tucson',
            '팰리세이드' => 'Palisade',
            '아이오닉' => 'Ioniq'
        ],
        'Kia' => [
            'K5' => 'K5/Optima',
            'K7' => 'K7/Cadenza',
            'K9' => 'K9/Quoris',
            '스포티지' => 'Sportage',
            '쏘렌토' => 'Sorento',
            '카니발' => 'Carnival/Sedona',
            '모닝' => 'Morning/Picanto'
        ]
    ];

    $translatedBrand = translateBrand($brand);

    if (isset($modelTranslations[$translatedBrand])) {
        return $modelTranslations[$translatedBrand][$model] ?? $model;
    }

    return $model;
}

// Перевод значений с корейского на русский
function translateKorean($text) {
    if (empty($text)) return "";

    $translations = [
        '자동' => 'Автомат',
        '수동' => 'Механика',
        '더블클러치' => 'Робот (DCT)',
        '무단변속기' => 'Вариатор (CVT)',
        '가솔린' => 'Бензин',
        '디젤' => 'Дизель',
        '하이브리드' => 'Гибрид',
        '전기' => 'Электро',
        '가스' => 'Газ'
    ];

    if (isset($translations[$text])) {
        return $translations[$text];
    }

    foreach ($translations as $korean => $russian) {
        if (strpos($text, $korean) !== false) {
            $text = str_replace($korean, $russian, $text);
        }
    }

    return $text;
}

// Форматирование цены для отображения
function formatPrice($price) {
    if (empty($price)) return "Цена не указана";

    $price = (int)$price;
    if ($price < 1000) return "$" . $price;

    return "$" . number_format($price, 0, '.', ' ');
}

// Форматирование пробега
function formatMileage($mileage) {
    if (empty($mileage) || $mileage === '0' || $mileage === 0) {
        return "Без пробега";
    }

    if (is_string($mileage)) {
        if (strpos($mileage, 'км') !== false) {
            return $mileage;
        }
        $mileage = preg_replace('/[^0-9]/', '', $mileage);
    }

    if (empty($mileage) || intval($mileage) === 0) {
        return "Без пробега";
    }

    $mileage = intval($mileage);

    if ($mileage > 1000000) {
        $mileage = round($mileage / 1000);
    }

    return number_format($mileage, 0, '.', ' ') . " км";
}

// Форматирование информации о двигателе
function formatEngine($car) {
    $engine = [];

    if (!empty($car['displacement'])) {
        $displacement = (float)$car['displacement'];
        if ($displacement > 0) {
            if ($displacement > 100) {
                $displacement = $displacement / 1000;
            }
            $engine[] = number_format($displacement, 1, '.', '') . " л";
        }
    }

    if (!empty($car['fuel_type'])) {
        $fuelType = translateKorean($car['fuel_type']);
        $engine[] = $fuelType;
    }

    if (empty($engine)) {
        if (!empty($car['engine_type'])) {
            return translateKorean($car['engine_type']);
        }
        return "Нет данных";
    }

    return implode(' • ', $engine);
}

// Функция для получения данных из локальных CSV файлов
function fetchDataFromLocalCSV($date, $params) {
    $dataDir = __DIR__ . '/encar_data/';
    $result = [];

    logDebug("Looking for CSV files in: $dataDir");

    // Сначала ищем файлы с точным именем для даты
    $activeFile = $dataDir . 'encar_active_' . $date . '.csv';
    $removedFile = $dataDir . 'encar_removed_' . $date . '.csv';

    // Проверяем наличие файлов
    if (file_exists($activeFile)) {
        logDebug("Found active file: $activeFile");
        $activeData = processCSVFile($activeFile, $params);
        $result = array_merge($result, $activeData);
    }

    // Если не найдено файлов, ищем любые CSV файлы в директории
    if (empty($result)) {
        logDebug("No specific files found, searching for any CSV files");
        $csvFiles = glob($dataDir . '*.csv');

        if (!empty($csvFiles)) {
            $csvFile = $csvFiles[0];
            logDebug("Using CSV file: $csvFile");
            $result = processCSVFile($csvFile, $params);
        } else {
            // Если CSV файлов нет, ищем файлы за предыдущие дни
            for ($i = 1; $i <= 7; $i++) {
                $prevDate = date('Y-m-d', strtotime("-$i days"));
                $prevActiveFile = $dataDir . 'encar_active_' . $prevDate . '.csv';

                if (file_exists($prevActiveFile)) {
                    logDebug("Using data from previous date: $prevDate");
                    $activeData = processCSVFile($prevActiveFile, $params);
                    $result = array_merge($result, $activeData);
                    break;
                }
            }
        }
    }

    logDebug("Total records found: " . count($result));
    return $result;
}

// Функция для обработки CSV файла
function processCSVFile($file, $params) {
    logDebug("Processing CSV file: $file");

    $handle = fopen($file, "r");
    if (!$handle) {
        logDebug("Failed to open file: $file");
        return [];
    }

    // Получаем курсы валют один раз для всего файла
    $exchangeRates = getExchangeRates();

    // Определяем разделитель CSV файла
    $firstLine = fgets($handle);
    rewind($handle);

    $delimiter = "|"; // По умолчанию
    if (substr_count($firstLine, ",") > substr_count($firstLine, "|")) {
        $delimiter = ",";
        logDebug("Using comma delimiter");
    } else {
        logDebug("Using pipe delimiter");
    }

    // Получаем заголовки
    $headers = fgetcsv($handle, 0, $delimiter);
    if (!$headers) {
        fclose($handle);
        logDebug("Failed to read headers from file: $file");
        return [];
    }

    logDebug("Headers found: " . implode(", ", array_slice($headers, 0, 10)) . (count($headers) > 10 ? "..." : ""));

    // Фильтруем и обрабатываем данные
    $result = [];
    $count = 0;
    $processed = 0;
    $reservedCarsSkipped = 0;

    while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
        // Пропускаем строки с неправильным количеством полей
        if (count($data) !== count($headers)) continue;

        $car = array_combine($headers, $data);

        // Фильтрация забронированных и лизинговых автомобилей
        $carPriceKRW = (int)($car['price'] ?? 0);
        if ($carPriceKRW === 999999 || $carPriceKRW === 333333) {
            $reservedCarsSkipped++;
            continue;
        }

        // Проверяем лимит - раннее прерывание для производительности
        if ($count >= $params['limit']) break;

        // Добавляем данные для отображения на сайте
        $car['price_usd'] = convertKRWToUSD($carPriceKRW, $exchangeRates);
        $car['price_formatted'] = formatPrice($car['price_usd']);
        $car['mileage_formatted'] = formatMileage($car['mileage'] ?? 0);
        $car['year'] = $car['year'] ?? '';
        $car['engine'] = formatEngine($car);

        // Переводим марку и модель
        if (!empty($car['mark'])) {
            $car['translated_mark'] = translateBrand($car['mark']);
            $car['brand'] = $car['translated_mark'];
            $car['mark'] = $car['translated_mark'];
        }

        if (!empty($car['model'])) {
            $car['translated_model'] = translateModel($car['brand'] ?? $car['mark'], $car['model']);
            $car['model'] = $car['translated_model'];
        }

        // Переводим значения с корейского
        if (!empty($car['transmission_type'])) {
            $car['transmission_type'] = translateKorean($car['transmission_type']);
        }

        $car['transmission'] = $car['transmission_type'] ?? '';
        $car['is_active'] = true;
        $car['local_image'] = '';

        // Добавляем в результат
        $result[] = $car;
        $count++;
        $processed++;
    }

    fclose($handle);

    logDebug("Processed $processed rows, returned $count cars, skipped $reservedCarsSkipped reserved cars");
    return $result;
}

// Функция для определения приоритета бренда (1-4, где 1 - высший приоритет)
function getBrandPriority($brand) {
    // Переводим марку для правильного сравнения
    $translatedBrand = translateBrand($brand);

    // 4-уровневая система приоритетов брендов
    $brandPriorities = [
        // 1 категория - Супер премиум люкс
        1 => [
            'Rolls-Royce', 'Bentley', 'Ferrari', 'Lamborghini', 'McLaren',
            'Bugatti', 'Koenigsegg', 'Pagani', 'Maybach', 'Mercedes-Maybach',
            'Brabus', 'Alpina', 'Lucid Motors', 'Rimac', 'Pininfarina', 'Spyker',
            'Alfa Romeo', 'Maserati'
        ],
        // 2 категория - Премиум
        2 => [
            'Mercedes-Benz', 'BMW', 'Audi', 'Porsche', 'Lexus', 'Cadillac',
            'Jaguar', 'Land Rover', 'Range Rover', 'Acura', 'Aston Martin',
            'Dodge', 'Tesla'
        ],
        // 3 категория - Средние
        3 => [
            'Toyota', 'Honda', 'Nissan', 'Mazda', 'Subaru', 'Mitsubishi',
            'Volvo', 'Jeep', 'Chrysler', 'Buick', 'GMC', 'Chevrolet',
            'Ford', 'Genesis', 'Hummer', 'Infiniti', 'Lincoln', 'Lotus',
            'MINI', 'Volkswagen'
        ],
        // 4 категория - Бюджетные
        4 => [
            'ChevroletGMDaewoo', 'Daewoo', 'Lada', 'Dacia', 'SEAT', 'Opel', 'Isuzu'
        ]
    ];

    // Ищем бренд в категориях приоритета
    foreach ($brandPriorities as $priority => $brands) {
        foreach ($brands as $priorityBrand) {
            if (strcasecmp($translatedBrand, $priorityBrand) === 0) {
                return $priority;
            }
        }
    }

    // Если бренд не найден, возвращаем самый низкий приоритет
    return 4;
}

// Функция для определения приоритета модели внутри бренда
function getModelPriority($brand, $model) {
    $translatedBrand = translateBrand($brand);

    // Приоритеты моделей для премиум брендов (чем меньше число, тем выше приоритет)
    $modelPriorities = [
        'Ferrari' => [
            'LaFerrari' => 1, 'SF90' => 2, '812' => 3, 'F8' => 4, 'Roma' => 5,
            'Portofino' => 6, '488' => 7, '458' => 8, 'California' => 9, 'F12' => 10
        ],
        'Lamborghini' => [
            'Revuelto' => 1, 'Aventador' => 2, 'Huracan' => 3, 'Urus' => 4,
            'Gallardo' => 5, 'Murcielago' => 6
        ],
        'Rolls-Royce' => [
            'Phantom' => 1, 'Cullinan' => 2, 'Ghost' => 3, 'Wraith' => 4,
            'Dawn' => 5, 'Spectre' => 6
        ],
        'Bentley' => [
            'Mulsanne' => 1, 'Flying Spur' => 2, 'Continental GT' => 3,
            'Bentayga' => 4, 'Continental' => 5
        ],
        'Mercedes-Benz' => [
            'AMG GT' => 1, 'S-Class' => 2, 'G-Class' => 3, 'E-Class' => 4,
            'C-Class' => 5, 'GLS' => 6, 'GLE' => 7, 'GLC' => 8
        ],
        'BMW' => [
            'M8' => 1, 'M5' => 2, 'M4' => 3, 'M3' => 4, 'M2' => 5,
            'X7' => 6, 'X6' => 7, 'X5' => 8, '7 Series' => 9, '5 Series' => 10
        ],
        'Audi' => [
            'R8' => 1, 'RS7' => 2, 'RS6' => 3, 'RS5' => 4, 'RS3' => 5,
            'Q8' => 6, 'Q7' => 7, 'A8' => 8, 'A7' => 9, 'A6' => 10
        ],
        'Porsche' => [
            '911' => 1, 'Taycan' => 2, 'Panamera' => 3, 'Cayenne' => 4,
            'Macan' => 5, '718' => 6, 'Boxster' => 7, 'Cayman' => 8
        ]
    ];

    if (isset($modelPriorities[$translatedBrand])) {
        foreach ($modelPriorities[$translatedBrand] as $priorityModel => $priority) {
            if (stripos($model, $priorityModel) !== false) {
                return $priority;
            }
        }
    }

    // Если модель не найдена в приоритетах, возвращаем средний приоритет
    return 50;
}

/**
 * 🚀 МОЩНАЯ ФУНКЦИЯ ПОЛНОГО КЭШИРОВАНИЯ CSV БАЗЫ ДАННЫХ
 *
 * Выполняет полную обработку CSV файла и создает:
 * 1. Разбивку по 4 категориям приоритета
 * 2. Индексы всех марок для быстрого поиска
 * 3. Индексы моделей по маркам
 * 4. Ценовые диапазоны по категориям
 * 5. Годовые диапазоны по категориям
 */
function superFastCacheGeneration($date) {
    $results = [
        'success' => true,
        'message' => 'Super fast cache generation completed successfully',
        'categories' => [],
        'brand_index' => [],
        'model_index' => [],
        'price_ranges' => [],
        'year_ranges' => [],
        'total_cars' => 0,
        'execution_time' => 0,
        'memory_usage' => 0,
        'cache_files' => []
    ];

    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);

    try {
        logDebug("=== SUPER FAST CACHE GENERATION START ===");
        logDebug("Processing CSV file for date: $date");

        // ЭТАП 0: Полная очистка старого кэша
        logDebug("STAGE 0: Clearing all existing cache files...");
        $clearedFiles = clearAllCache();
        logDebug("Cleared " . count($clearedFiles) . " old cache files");

        // Загружаем ВСЕ автомобили без лимитов
        $params = [
            'date' => $date,
            'limit' => 999999, // Максимальный лимит
            'offset' => 0
        ];

        $allCars = fetchDataFromLocalCSV($date, $params);
        $totalCars = count($allCars);
        logDebug("Loaded $totalCars cars from CSV");

        // Создаем директорию для кэша
        $cacheDir = __DIR__ . '/../../cache';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0777, true);
        }

        // Инициализируем структуры данных
        $categorizedCars = [1 => [], 2 => [], 3 => [], 4 => []];
        $brandIndex = [];
        $modelIndex = [];
        $priceRanges = [1 => [], 2 => [], 3 => [], 4 => []];
        $yearRanges = [1 => [], 2 => [], 3 => [], 4 => []];

        // ЭТАП 1: Обрабатываем каждый автомобиль и распределяем по категориям
        logDebug("STAGE 1: Categorizing cars by brand priority...");
        foreach ($allCars as $car) {
            $brand = $car['brand'] ?? $car['mark'] ?? '';
            $model = $car['model'] ?? '';
            $year = (int)($car['year'] ?? 0);
            $price = (int)($car['price'] ?? 0);

            // Определяем приоритет бренда
            $priority = getBrandPriority($brand);

            // Добавляем автомобиль в соответствующую категорию
            $categorizedCars[$priority][] = $car;

            // Индексируем бренды
            if (!isset($brandIndex[$brand])) {
                $brandIndex[$brand] = [
                    'priority' => $priority,
                    'count' => 0,
                    'models' => [],
                    'price_min' => PHP_INT_MAX,
                    'price_max' => 0,
                    'year_min' => PHP_INT_MAX,
                    'year_max' => 0
                ];
            }
            $brandIndex[$brand]['count']++;

            // Индексируем модели
            if (!empty($model)) {
                if (!isset($modelIndex[$brand])) {
                    $modelIndex[$brand] = [];
                }
                if (!isset($modelIndex[$brand][$model])) {
                    $modelIndex[$brand][$model] = 0;
                }
                $modelIndex[$brand][$model]++;

                if (!in_array($model, $brandIndex[$brand]['models'])) {
                    $brandIndex[$brand]['models'][] = $model;
                }
            }

            // Обновляем диапазоны цен и годов
            if ($price > 0) {
                $brandIndex[$brand]['price_min'] = min($brandIndex[$brand]['price_min'], $price);
                $brandIndex[$brand]['price_max'] = max($brandIndex[$brand]['price_max'], $price);
                $priceRanges[$priority][] = $price;
            }

            if ($year > 0) {
                $brandIndex[$brand]['year_min'] = min($brandIndex[$brand]['year_min'], $year);
                $brandIndex[$brand]['year_max'] = max($brandIndex[$brand]['year_max'], $year);
                $yearRanges[$priority][] = $year;
            }
        }

        // ЭТАП 2: Сортируем автомобили в каждой категории по приоритету
        logDebug("STAGE 2: Sorting cars within each category...");
        foreach ($categorizedCars as $priority => &$cars) {
            if (!empty($cars)) {
                usort($cars, function($a, $b) {
                    // Сортируем по приоритету модели → год → цена
                    $brandA = $a['brand'] ?? $a['mark'] ?? '';
                    $brandB = $b['brand'] ?? $b['mark'] ?? '';
                    $modelA = $a['model'] ?? '';
                    $modelB = $b['model'] ?? '';

                    $modelPriorityA = getModelPriority($brandA, $modelA);
                    $modelPriorityB = getModelPriority($brandB, $modelB);

                    if ($modelPriorityA !== $modelPriorityB) {
                        return $modelPriorityA - $modelPriorityB;
                    }

                    $yearA = (int)($a['year'] ?? 0);
                    $yearB = (int)($b['year'] ?? 0);
                    if ($yearA !== $yearB) {
                        return $yearB - $yearA; // Новые сначала
                    }

                    $priceA = (int)($a['price'] ?? 0);
                    $priceB = (int)($b['price'] ?? 0);
                    return $priceA - $priceB; // Дешевые сначала
                });
            }
        }

        // ЭТАП 3: Создаем кэш-файлы для каждой категории (разбиваем большие файлы на части)
        logDebug("STAGE 3: Creating chunked cache files...");
        foreach ($categorizedCars as $priority => $cars) {
            $categoryName = getPriorityName($priority);
            $carCount = count($cars);

            // Определяем размер чанка в зависимости от приоритета
            $chunkSize = getChunkSizeForPriority($priority, $carCount);
            $chunks = array_chunk($cars, $chunkSize);
            $chunkCount = count($chunks);

            logDebug("Priority $priority ($categoryName): $carCount cars, splitting into $chunkCount chunks of max $chunkSize cars each");

            $results['categories'][$priority] = [
                'name' => $categoryName,
                'count' => $carCount,
                'chunks' => $chunkCount,
                'chunk_size' => $chunkSize,
                'cache_files' => []
            ];

            // Создаем файл для каждого чанка
            foreach ($chunks as $chunkIndex => $chunkCars) {
                $chunkNumber = $chunkIndex + 1;
                $cacheFile = $cacheDir . "/super_priority_{$priority}_chunk_{$chunkNumber}_{$date}.json";

                $cacheData = [
                    'priority' => $priority,
                    'category_name' => $categoryName,
                    'chunk_number' => $chunkNumber,
                    'total_chunks' => $chunkCount,
                    'date' => $date,
                    'cars' => $chunkCars,
                    'count' => count($chunkCars),
                    'generated_at' => date('Y-m-d H:i:s'),
                    'expires_at' => date('Y-m-d H:i:s', strtotime('+1 day')),
                    'version' => '2.0_chunked'
                ];

                file_put_contents($cacheFile, json_encode($cacheData, JSON_UNESCAPED_UNICODE));

                $fileSize = filesize($cacheFile);
                $results['categories'][$priority]['cache_files'][] = [
                    'file' => basename($cacheFile),
                    'chunk' => $chunkNumber,
                    'cars' => count($chunkCars),
                    'size' => $fileSize,
                    'size_mb' => round($fileSize / 1024 / 1024, 2)
                ];

                $results['cache_files'][] = $cacheFile;
                logDebug("Created chunk $chunkNumber/$chunkCount for priority $priority: " . count($chunkCars) . " cars, " . formatBytes($fileSize));
            }
        }

        // ЭТАП 4: Создаем индекс брендов
        logDebug("STAGE 4: Creating brand index...");
        $brandIndexFile = $cacheDir . "/brand_index_{$date}.json";
        file_put_contents($brandIndexFile, json_encode($brandIndex, JSON_UNESCAPED_UNICODE));
        $results['cache_files'][] = $brandIndexFile;

        // ЭТАП 5: Создаем индекс моделей
        logDebug("STAGE 5: Creating model index...");
        $modelIndexFile = $cacheDir . "/model_index_{$date}.json";
        file_put_contents($modelIndexFile, json_encode($modelIndex, JSON_UNESCAPED_UNICODE));
        $results['cache_files'][] = $modelIndexFile;

        // ЭТАП 6: Создаем статистику по ценам и годам
        logDebug("STAGE 6: Creating price and year statistics...");
        foreach ($priceRanges as $priority => &$prices) {
            if (!empty($prices)) {
                sort($prices);
                $priceRanges[$priority] = [
                    'min' => min($prices),
                    'max' => max($prices),
                    'median' => $prices[count($prices) / 2],
                    'count' => count($prices)
                ];
            }
        }

        foreach ($yearRanges as $priority => &$years) {
            if (!empty($years)) {
                sort($years);
                $yearRanges[$priority] = [
                    'min' => min($years),
                    'max' => max($years),
                    'median' => $years[count($years) / 2],
                    'count' => count($years)
                ];
            }
        }

        // ЭТАП 7: Создаем мастер-индекс
        logDebug("STAGE 7: Creating master index...");
        $masterIndexFile = $cacheDir . "/master_index_{$date}.json";
        $masterIndex = [
            'date' => $date,
            'total_cars' => $totalCars,
            'categories' => $results['categories'],
            'brand_count' => count($brandIndex),
            'price_ranges' => $priceRanges,
            'year_ranges' => $yearRanges,
            'generated_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 day')),
            'version' => '2.0_super_fast'
        ];

        file_put_contents($masterIndexFile, json_encode($masterIndex, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        $results['cache_files'][] = $masterIndexFile;

        $results['total_cars'] = $totalCars;
        $results['brand_index'] = array_keys($brandIndex);
        $results['model_index'] = array_keys($modelIndex);
        $results['price_ranges'] = $priceRanges;
        $results['year_ranges'] = $yearRanges;

    } catch (Exception $e) {
        $results['success'] = false;
        $results['message'] = 'Super fast cache generation failed: ' . $e->getMessage();
        logDebug("Cache generation error: " . $e->getMessage());
    }

    $results['execution_time'] = round(microtime(true) - $startTime, 2);
    $results['memory_usage'] = memory_get_usage(true) - $startMemory;

    logDebug("=== SUPER FAST CACHE GENERATION END ===");
    logDebug("Execution time: " . $results['execution_time'] . " seconds");
    logDebug("Memory usage: " . formatBytes($results['memory_usage']));

    return $results;
}

/**
 * Функция для получения названия категории по приоритету
 */
function getPriorityName($priority) {
    $names = [
        1 => 'Супер премиум люкс',
        2 => 'Премиум',
        3 => 'Средние',
        4 => 'Бюджетные'
    ];

    return $names[$priority] ?? 'Неизвестная категория';
}

/**
 * Функция для определения размера чанка в зависимости от приоритета и количества автомобилей
 */
function getChunkSizeForPriority($priority, $carCount) {
    // Целевой размер файла: ~50MB (примерно 10,000-15,000 автомобилей)
    $targetChunkSize = 12000;

    switch ($priority) {
        case 1: // Супер премиум - обычно мало автомобилей, один файл
            return max($carCount, $targetChunkSize);

        case 2: // Премиум - может быть много, разбиваем на части по 12k
            return $targetChunkSize;

        case 3: // Средние - разбиваем на части по 12k
            return $targetChunkSize;

        case 4: // Бюджетные - самая большая категория, разбиваем на 5 частей минимум
            $minChunks = 5;
            $calculatedChunkSize = ceil($carCount / $minChunks);
            return min($calculatedChunkSize, $targetChunkSize);

        default:
            return $targetChunkSize;
    }
}

/**
 * Функция для форматирования размера файла
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * Функция для ПОЛНОЙ очистки папки кэша
 */
function clearAllCache() {
    $cacheDir = __DIR__ . '/../../cache';
    $cleaned = [];

    if (!is_dir($cacheDir)) {
        logDebug("Cache directory does not exist: $cacheDir");
        return $cleaned;
    }

    logDebug("=== CLEARING ALL CACHE FILES ===");

    // Удаляем все JSON файлы в папке кэша
    $files = glob($cacheDir . '/*.json');

    foreach ($files as $file) {
        $filename = basename($file);
        if (unlink($file)) {
            $cleaned[] = $filename;
            logDebug("Deleted cache file: $filename");
        } else {
            logDebug("Failed to delete cache file: $filename");
        }
    }

    logDebug("Total cache files deleted: " . count($cleaned));
    return $cleaned;
}

/**
 * Функция для очистки старых кэш-файлов
 */
function cleanOldCache($keepDays = 3) {
    $cacheDir = __DIR__ . '/../../cache';
    $cleaned = [];

    if (!is_dir($cacheDir)) {
        return $cleaned;
    }

    $files = glob($cacheDir . '/priority_*.json');
    $cutoffTime = time() - ($keepDays * 24 * 60 * 60);

    foreach ($files as $file) {
        if (filemtime($file) < $cutoffTime) {
            $cleaned[] = basename($file);
            unlink($file);
        }
    }

    return $cleaned;
}

/**
 * 📦 СТАРАЯ ФУНКЦИЯ КЭШИРОВАНИЯ (для совместимости)
 */
function warmCacheByPriorities($date) {
    $results = [
        'success' => true,
        'message' => 'Basic cache warming completed successfully',
        'categories' => [],
        'total_cars' => 0,
        'execution_time' => 0,
        'memory_usage' => 0
    ];

    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);

    try {
        // Параметры для загрузки всех автомобилей
        $params = [
            'date' => $date,
            'limit' => 10000,
            'offset' => 0,
            'priority_sort' => 'true'
        ];

        logDebug("=== BASIC CACHE WARMER START ===");
        logDebug("Loading all cars with priority sorting for date: $date");

        $allCars = fetchDataFromLocalCSV($date, $params);
        logDebug("Loaded " . count($allCars) . " cars total");

        // Разделяем автомобили по категориям приоритета
        $categorizedCars = [1 => [], 2 => [], 3 => [], 4 => []];

        foreach ($allCars as $car) {
            $brand = $car['brand'] ?? $car['mark'] ?? '';
            $priority = getBrandPriority($brand);
            $categorizedCars[$priority][] = $car;
        }

        // Создаем кэш-файлы для каждой категории
        $cacheDir = __DIR__ . '/../../cache';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0777, true);
        }

        foreach ($categorizedCars as $priority => $cars) {
            $categoryName = getPriorityName($priority);
            $cacheFile = $cacheDir . "/priority_{$priority}_{$date}.json";

            $cacheData = [
                'priority' => $priority,
                'category_name' => $categoryName,
                'date' => $date,
                'cars' => $cars,
                'count' => count($cars),
                'generated_at' => date('Y-m-d H:i:s'),
                'expires_at' => date('Y-m-d H:i:s', strtotime('+1 day'))
            ];

            file_put_contents($cacheFile, json_encode($cacheData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            $results['categories'][$priority] = [
                'name' => $categoryName,
                'count' => count($cars),
                'cache_file' => $cacheFile,
                'file_size' => filesize($cacheFile)
            ];

            logDebug("Created cache for priority $priority ($categoryName): " . count($cars) . " cars");
        }

        $results['total_cars'] = count($allCars);

    } catch (Exception $e) {
        $results['success'] = false;
        $results['message'] = 'Cache warming failed: ' . $e->getMessage();
        logDebug("Cache warming error: " . $e->getMessage());
    }

    $results['execution_time'] = round(microtime(true) - $startTime, 2);
    $results['memory_usage'] = memory_get_usage(true) - $startMemory;

    logDebug("=== BASIC CACHE WARMER END ===");
    logDebug("Execution time: " . $results['execution_time'] . " seconds");
    logDebug("Memory usage: " . formatBytes($results['memory_usage']));

    return $results;
}

/**
 * 🔍 ФУНКЦИЯ БЫСТРОГО ПОИСКА ПО КЭШИРОВАННЫМ ДАННЫМ (с поддержкой чанков)
 */
function fastSearchInCache($date, $searchParams) {
    $cacheDir = __DIR__ . '/../../cache';
    $results = [];

    // Загружаем мастер-индекс
    $masterIndexFile = $cacheDir . "/master_index_{$date}.json";
    if (!file_exists($masterIndexFile)) {
        return ['error' => 'Master index not found. Run cache generation first.'];
    }

    $masterIndex = json_decode(file_get_contents($masterIndexFile), true);

    // Если указан бренд, ищем в соответствующей категории
    if (!empty($searchParams['brand'])) {
        $brandIndexFile = $cacheDir . "/brand_index_{$date}.json";
        if (file_exists($brandIndexFile)) {
            $brandIndex = json_decode(file_get_contents($brandIndexFile), true);

            foreach ($brandIndex as $brand => $info) {
                if (stripos($brand, $searchParams['brand']) !== false) {
                    $priority = $info['priority'];

                    // Ищем все чанки для этого приоритета
                    $chunkFiles = glob($cacheDir . "/super_priority_{$priority}_chunk_*_{$date}.json");

                    foreach ($chunkFiles as $chunkFile) {
                        $categoryData = json_decode(file_get_contents($chunkFile), true);

                        foreach ($categoryData['cars'] as $car) {
                            $carBrand = $car['brand'] ?? $car['mark'] ?? '';
                            if (stripos($carBrand, $searchParams['brand']) !== false) {
                                $results[] = $car;
                            }
                        }
                    }
                }
            }
        }
    } else {
        // Если бренд не указан, ищем во всех категориях в порядке приоритета
        for ($priority = 1; $priority <= 4; $priority++) {
            $chunkFiles = glob($cacheDir . "/super_priority_{$priority}_chunk_*_{$date}.json");

            foreach ($chunkFiles as $chunkFile) {
                $categoryData = json_decode(file_get_contents($chunkFile), true);
                $results = array_merge($results, $categoryData['cars']);
            }
        }
    }

    return [
        'success' => true,
        'results' => $results,
        'count' => count($results),
        'search_params' => $searchParams
    ];
}

// Основная логика выполнения
try {
    $action = $_GET['action'] ?? 'super_warm';
    $date = $_GET['date'] ?? date('Y-m-d');

    switch ($action) {
        case 'super_warm':
            // НОВАЯ МОЩНАЯ ФУНКЦИЯ КЭШИРОВАНИЯ
            $result = superFastCacheGeneration($date);
            break;

        case 'warm':
            // Старая функция для совместимости
            $result = warmCacheByPriorities($date);
            break;

        case 'search':
            // Быстрый поиск по кэшированным данным
            $searchParams = [
                'brand' => $_GET['brand'] ?? '',
                'model' => $_GET['model'] ?? '',
                'year_from' => $_GET['year_from'] ?? '',
                'year_to' => $_GET['year_to'] ?? '',
                'price_from' => $_GET['price_from'] ?? '',
                'price_to' => $_GET['price_to'] ?? ''
            ];
            $result = fastSearchInCache($date, $searchParams);
            break;

        case 'brands':
            // Получение списка всех брендов
            $cacheDir = __DIR__ . '/../../cache';
            $brandIndexFile = $cacheDir . "/brand_index_{$date}.json";
            if (file_exists($brandIndexFile)) {
                $brandIndex = json_decode(file_get_contents($brandIndexFile), true);
                $result = [
                    'success' => true,
                    'brands' => array_keys($brandIndex),
                    'brand_details' => $brandIndex,
                    'count' => count($brandIndex)
                ];
            } else {
                $result = [
                    'success' => false,
                    'message' => 'Brand index not found. Run cache generation first.'
                ];
            }
            break;

        case 'models':
            // Получение моделей для указанного бренда
            $brand = $_GET['brand'] ?? '';
            $cacheDir = __DIR__ . '/../../cache';
            $modelIndexFile = $cacheDir . "/model_index_{$date}.json";
            if (file_exists($modelIndexFile) && !empty($brand)) {
                $modelIndex = json_decode(file_get_contents($modelIndexFile), true);
                $result = [
                    'success' => true,
                    'brand' => $brand,
                    'models' => $modelIndex[$brand] ?? [],
                    'count' => count($modelIndex[$brand] ?? [])
                ];
            } else {
                $result = [
                    'success' => false,
                    'message' => 'Model index not found or brand not specified.'
                ];
            }
            break;

        case 'clean':
            $keepDays = (int)($_GET['keep_days'] ?? 3);
            $cleaned = cleanOldCache($keepDays);
            $result = [
                'success' => true,
                'message' => 'Cache cleanup completed',
                'cleaned_files' => $cleaned,
                'count' => count($cleaned)
            ];
            break;

        case 'clear_all':
            // Полная очистка всего кэша
            $cleared = clearAllCache();
            $result = [
                'success' => true,
                'message' => 'All cache files cleared successfully',
                'cleared_files' => $cleared,
                'count' => count($cleared)
            ];
            break;

        case 'status':
            $cacheDir = __DIR__ . '/../../cache';
            $files = glob($cacheDir . '/*.json');
            $result = [
                'success' => true,
                'message' => 'Cache status retrieved',
                'cache_files' => array_map('basename', $files),
                'count' => count($files),
                'cache_dir' => $cacheDir,
                'total_size' => array_sum(array_map('filesize', $files))
            ];
            break;

        default:
            $result = [
                'success' => false,
                'message' => 'Unknown action. Available actions: super_warm, warm, search, brands, models, clean, clear_all, status'
            ];
    }

    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Cache warmer error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
